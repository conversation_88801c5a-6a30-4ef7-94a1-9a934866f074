import type { <PERSON>a, StoryObj } from "@storybook/react-vite";
import RsdsLogo from "./RsdsLogo";

const meta: Meta<typeof RsdsLogo> = {
	title: "Other/RsdsLogo",
	component: RsdsLogo,
	parameters: {
		layout: "centered",
	},
	tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof RsdsLogo>;

export const Primary: Story = {
	args: {
		variant: "black-gray",
		children: "Button",
	},
};
