import * as React from "react";
import "../../styles/public/tailwind.css";

// Simple className utility function
const cn = (...classes: (string | undefined | null | false)[]): string => {
	return classes.filter(Boolean).join(" ");
};

// Logo variant configurations
const logoVariants = {
	variant: {
		color: "",
		"black-gray": "",
		"white-darkmode": "",
		"black-gray-darkmode": "",
	},
	size: {
		sm: "h-[7.5rem] w-8", // 32px width, maintaining aspect ratio
		md: "h-[15rem] w-16", // 64px width (original size)
		lg: "h-[22.5rem] w-24", // 96px width
		xl: "h-[30rem] w-32", // 128px width
	},
};

// Helper function to get logo classes
const getLogoClasses = (
	variant: keyof typeof logoVariants.variant = "color",
	size: keyof typeof logoVariants.size = "md",
	className?: string
): string => {
	return cn(
		"inline-block",
		logoVariants.variant[variant],
		logoVariants.size[size],
		className
	);
};

export interface LogoProps extends React.SVGAttributes<SVGSVGElement> {
	/**
	 * Logo variant
	 */
	variant?: keyof typeof logoVariants.variant;
	/**
	 * Logo size
	 */
	size?: keyof typeof logoVariants.size;
	/**
	 * Alternative text for accessibility
	 */
	alt?: string;
}

const RsdsLogo = React.forwardRef<SVGSVGElement, LogoProps>(
	({ className, variant = "color", size = "md", alt = "RS Design System Logo", ...props }, ref) => {
		const logoClass = getLogoClasses(variant, size, className);

		// Define colors based on variant
		const colors = {
			color: { primary: "#FDD32F", secondary: "#E40135" },
			"black-gray": { primary: "#202020", secondary: "#202020" },
			"white-darkmode": { primary: "white", secondary: "white" },
			"black-gray-darkmode": { primary: "white", secondary: "white" },
		};

		const { primary, secondary } = colors[variant || "color"];

		return (
			<svg
				ref={ref}
				width="64"
				height="60"
				viewBox="0 0 64 60"
				fill="none"
				xmlns="http://www.w3.org/2000/svg"
				className={logoClass}
				role="img"
				aria-label={alt}
				{...props}
			>
				{/* Yellow/Primary colored paths */}
				<path d="M28.2086 16.9813C29.3407 16.4919..." fill={primary} />
				<path d="M25.9503 18.156L16.8395 6.10535C13.9462..." fill={primary} />
				<path d="M41.2893 18.3587C44.0428 24.0036..." fill={primary} />
				<path d="M39.2984 16.3929L47.1774 6.17422C44.2907..." fill={primary} />

				{/* Red/Secondary colored paths */}
				<path d="M30.2972 41.5621C28.9893 41.5621..." fill={secondary} />
				{/* ... other red paths */}
			</svg>
		);
	},
);
RsdsLogo.displayName = "Logo";

export { RsdsLogo };
export default RsdsLogo;
